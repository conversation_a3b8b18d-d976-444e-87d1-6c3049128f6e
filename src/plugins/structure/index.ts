import type { Editor, Plugin } from '../../types';
// Import all structure plugins
import headingsPlugin from './headings';
import bulletListPlugin from './bullet-list';
import numberedListPlugin from './numbered-list';
import checklistPlugin from './checklist';
import indentPlugin from './indent';
import outdentPlugin from './outdent';
import alignmentPlugin from './alignment';
import lineHeightPlugin from './line-height';
import blockquotePlugin from './blockquote';
import horizontalRulePlugin from './horizontal-rule';
import codeBlockPlugin from './code-block';
import tablePlugin from './table';
import collapsiblePlugin from './collapsible';

// This file serves as the entry point for all structure plugins
// Each plugin is exported individually to ensure tree-shaking works properly

// Export individual plugins
export { 
  headingsPlugin,
  bulletListPlugin,
  numberedListPlugin,
  checklistPlugin,
  indentPlugin,
  outdentPlugin,
  alignmentPlugin,
  lineHeightPlugin,
  blockquotePlugin,
  horizontalRulePlugin,
  codeBlockPlugin,
  tablePlugin,
  collapsiblePlugin
};

// Export a combined plugin group for convenience
export const StructurePlugins: Plugin[] = [
  headingsPlugin,
  bulletListPlugin,
  numberedListPlugin,
  checklistPlugin,
  indentPlugin,
  outdentPlugin,
  alignmentPlugin,
  lineHeightPlugin,
  blockquotePlugin,
  horizontalRulePlugin,
  codeBlockPlugin,
  tablePlugin,
  collapsiblePlugin
];

// Export the plugins array directly
// PluginManager will handle individual plugin registration
export default StructurePlugins;
