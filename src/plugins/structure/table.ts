/**
 * Table Plugin for FeatherJS ▢▢▢
 * ──────────────────────────────
 * ❶  Toolbar “Table” opens a 10 × 10 grid-picker so authors can
 *    choose the initial size with the mouse.
 * ❷  When the caret is inside any table, a **floating mini-toolbar**
 *    appears allowing: add / delete row, add / delete column, delete table.
 * ❸  Pure DOM APIs – no external dependencies.
 * ❹  All listeners cleaned up via AbortController.
 */

import type { Editor } from '../../types';
import { BasePlugin, PluginConfig } from '../base-plugin';
import { ThemeManager } from '../../themes/theme-manager-legacy';

/* -------------------------------------------------------------------------- */
/*                                   Meta                                     */
/* -------------------------------------------------------------------------- */

const META: PluginConfig = {
  id:          'table',
  name:        'Table',
  description: 'Grid-picker table insertion and editing',
  version:     '1.0.0',
  toolbarItems: [
    {
      id: 'table',
      command: 'table',
      icon: '▢▢▢',
      label: 'Table',
      tooltip: 'Insert table',
      group: 'structure',
      ariaLabel: 'Insert table via grid picker'
    }
  ]
};

export const pluginId = META.id;

/* -------------------------------------------------------------------------- */
/*                               Config                                       */
/* -------------------------------------------------------------------------- */

interface TableConfig {
  gridPickerSize?: number; // n × n (default 10)
}
const DEFAULTS: Required<TableConfig> = { gridPickerSize: 10 };

/* -------------------------------------------------------------------------- */
/*                             Main class                                     */
/* -------------------------------------------------------------------------- */

class TablePlugin extends BasePlugin {
  private cfg: Required<TableConfig>;
  private abort = new AbortController();

  /** floating toolbar when caret inside table */
  private miniToolbar?: HTMLElement;

  constructor(userCfg: TableConfig = {}) {
    super(META);
    this.cfg = { ...DEFAULTS, ...userCfg };
  }

  /* ------------------------ Base hooks ------------------------- */

  protected override onInit(): void {
    /* show / hide mini-toolbar on selection changes */
    document.addEventListener(
      'selectionchange',
      () => this.updateMiniToolbar(),
      { signal: this.abort.signal }
    );

    /* listen for theme changes to update existing tables */
    window.addEventListener('feather:themechange', this.updateTablesTheme.bind(this), {
      signal: this.abort.signal
    });

    // Also listen for the document-level themechange event
    document.addEventListener('themechange', () => this.updateTablesTheme(), {
      signal: this.abort.signal
    });
  }

  /**
   * Update theme classes on all tables when theme changes
   */
  private updateTablesTheme(): void {
    if (!this.editor) return;

    const editorElement = this.editor.getElement();
    if (!editorElement) return;

    // Find all tables in the editor
    const tables = editorElement.querySelectorAll('table.feather-table');

    // Update theme classes on each table and its cells
    tables.forEach(table => {
      if (table instanceof HTMLElement) {
        // Apply theme to the table
        ThemeManager.applyThemeToElement(table);

        // Apply theme to all cells
        table.querySelectorAll('td, th').forEach(cell => {
          if (cell instanceof HTMLElement) {
            ThemeManager.applyThemeToElement(cell);
          }
        });
      }
    });
  }

  public override handleCommand(cmd: string): void {
    if (cmd === 'table') this.openGridPicker();
  }

  /* ---------------------- Grid picker UI ----------------------- */

  private openGridPicker(): void {
    /* overlay */
    const backdrop = document.createElement('div');
    backdrop.className = 'fixed inset-0 bg-black/25 z-[1000] flex items-center justify-center';

    const grid = document.createElement('div');
    grid.className = 'grid bg-white dark:bg-slate-800 p-3 rounded-lg gap-0.5 relative shadow-lg';

    const indicator = document.createElement('div');
    indicator.className = 'absolute -bottom-7 left-0 text-[13px] text-white bg-black/80 px-[6px] py-[2px] rounded-sm pointer-events-none';
    grid.appendChild(indicator);

    /* build cells */
    const max = this.cfg.gridPickerSize;
    grid.style.gridTemplateColumns = `repeat(${max}, minmax(0, 1fr))`;

    for (let r = 1; r <= max; r++) {
      for (let c = 1; c <= max; c++) {
        const cell = document.createElement('div');
        cell.className = 'border border-gray-300 dark:border-slate-600 rounded-sm cursor-pointer w-6 h-6 transition-colors duration-100 ease-in-out picker-cell';
        cell.dataset.row = String(r);
        cell.dataset.col = String(c);
        grid.appendChild(cell);
      }
    }

    /* hover */
    const activeCellClasses = ['bg-blue-400', 'dark:bg-blue-500', 'border-blue-600', 'dark:border-blue-400'];
    const inactiveCellClasses = ['border-gray-300', 'dark:border-slate-600'];

    grid.addEventListener('mouseover', (e) => {
      const target = (e.target as HTMLElement).closest('.picker-cell');
      if (!target) return;
      const rowNum = Number((target as HTMLElement).dataset.row);
      const colNum = Number((target as HTMLElement).dataset.col);

      grid.querySelectorAll<HTMLElement>('.picker-cell').forEach((c: HTMLElement) => {
        const r = Number(c.dataset.row);
        const cl = Number(c.dataset.col);
        const isActive = r <= rowNum && cl <= colNum;

        c.classList.remove(...activeCellClasses, ...inactiveCellClasses.filter(cls => !c.classList.contains(cls) && !activeCellClasses.includes(cls)));
        if (isActive) {
          c.classList.add(...activeCellClasses);
        } else {
          c.classList.add(...inactiveCellClasses.filter(cls => !c.classList.contains(cls)));
        }
      });
      indicator.textContent = `${rowNum} × ${colNum}`;
    });

    /* click – insert table */
    grid.addEventListener('click', (e) => {
      const target = (e.target as HTMLElement).closest('.picker-cell'); // Corrected selector
      if (!target) return;
      const rows = Number((target as HTMLElement).dataset.row);
      const cols = Number((target as HTMLElement).dataset.col);
      this.insertTable(rows, cols);
      backdrop.remove();
    });

    backdrop.appendChild(grid);
    this.editor?.getElement().appendChild(backdrop);

    /* esc closes */
    const esc = (ev: KeyboardEvent) => {
      if (ev.key === 'Escape') backdrop.remove();
    };
    document.addEventListener('keydown', esc, { once: true });
    }


  private insertTable(rows: number, cols: number): void {
    if (!this.editor) return;

    const table = document.createElement('table');
    table.className = 'feather-table border-collapse w-full my-1';

    // Apply theme classes to the table using ThemeManager
    ThemeManager.applyThemeToElement(table);

    const tbody = table.createTBody();
    for (let r = 0; r < rows; r++) {
        const row = tbody.insertRow();
        for (let c = 0; c < cols; c++) {
            const cell = row.insertCell();
            cell.className = 'border border-gray-300 dark:border-slate-600 p-1 min-w-[40px]';
            cell.innerHTML = '<br>';
            // Apply theme to each cell as well
            ThemeManager.applyThemeToElement(cell);
        }
    }

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
        this.editor.getElement().appendChild(table);
        const firstCellFallback = table.querySelector('td');
        if (firstCellFallback) placeCaret(firstCellFallback);
        this.editor.getElement().dispatchEvent(new InputEvent('input', { bubbles: true, cancelable: true }));
        return;
    }

    const range = selection.getRangeAt(0);

    if (!range.collapsed) {
        range.deleteContents();
    }

    if (this.editor.getElement().contains(range.commonAncestorContainer)) {
        range.insertNode(table);
        range.setStartAfter(table);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);

        const firstCell = table.querySelector('td');
        if (firstCell) {
            placeCaret(firstCell);
        }
        this.editor.getElement().dispatchEvent(new InputEvent('input', { bubbles: true, cancelable: true }));
    } else {
        this.editor.getElement().appendChild(table);
        const firstCellFallback = table.querySelector('td');
        if (firstCellFallback) {
            placeCaret(firstCellFallback);
        }
        this.editor.getElement().dispatchEvent(new InputEvent('input', { bubbles: true, cancelable: true }));
    }
  }

  /* -------------------- Floating mini-toolbar ------------------- */

  private updateMiniToolbar(): void {
    if (!this.editor) return;

    const cell = this.getActiveTableCell();
    const table = cell?.closest('table.feather-table');

    if (!table || !cell) {
      this.miniToolbar?.remove();
      this.miniToolbar = undefined;
      return;
    }

    const hiddenClasses = ['opacity-0', '-translate-y-2.5', 'pointer-events-none'];
    const visibleClasses = ['opacity-100', 'translate-y-0', 'pointer-events-auto'];

    if (!this.miniToolbar) {
      this.miniToolbar = this.buildMiniToolbar();
      this.miniToolbar.classList.add(...hiddenClasses.filter(c => c !== 'opacity-0'));
      document.body.appendChild(this.miniToolbar);
      void this.miniToolbar.offsetWidth;
    }

    this.miniToolbar.classList.remove(...hiddenClasses);
    this.miniToolbar.classList.add(...visibleClasses);

    const rect = table.getBoundingClientRect();
    this.miniToolbar.style.left = `${rect.left + window.scrollX}px`;
    this.miniToolbar.style.top  = `${rect.top  + window.scrollY - (this.miniToolbar.offsetHeight + 2)}px`;
  }

  private buildMiniToolbar(): HTMLElement {
    const bar = document.createElement('div');
    bar.className = 'absolute flex gap-1 bg-white dark:bg-slate-700 border border-gray-300 dark:border-slate-600 p-1 rounded shadow-md z-[100] transition-all duration-150 ease-in-out transform pointer-events-none opacity-0 -translate-y-2.5';

    const btn = (title: string, handler: () => void, label: string) => {
      const b = document.createElement('button');
      b.type = 'button';
      b.className = 'border border-gray-400 dark:border-slate-500 bg-gray-100 dark:bg-slate-600 text-gray-700 dark:text-slate-200 px-[6px] py-[2px] rounded-sm cursor-pointer text-xs transition-colors duration-100 ease-in-out hover:bg-gray-200 dark:hover:bg-slate-500 active:bg-gray-300 dark:active:bg-slate-400';
      b.title = title;
      b.textContent = label;
      b.addEventListener('click', (ev) => {
        ev.preventDefault();
        handler();
      });
      bar.appendChild(b);
    };

    btn('Insert row below', () => this.addRow('below'), '↧');
    btn('Insert row above', () => this.addRow('above'), '↥');
    btn('Insert column right', () => this.addColumn('right'), '→');
    btn('Insert column left', () => this.addColumn('left'), '←');
    btn('Delete row', () => this.deleteRow(), '🗑 row');
    btn('Delete column', () => this.deleteColumn(), '🗑 col');
    btn('Delete table', () => this.deleteTable(), '🗑 tbl');

    return bar;
  }

  /* ------------- table operations (CRUD) ------------- */

  private getActiveTableCell(): HTMLTableCellElement | null {
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0) return null;
    const container = sel.getRangeAt(0).startContainer;
    return (container instanceof HTMLElement
      ? container.closest('td, th')
      : container.parentElement?.closest('td, th')) as HTMLTableCellElement | null;
  }

  private addRow(where: 'above' | 'below'): void {
    const cell = this.getActiveTableCell();
    if (!cell) return;
    const row = cell.parentElement!;
    const newRow = row.cloneNode(true) as HTMLTableRowElement;
    newRow.querySelectorAll('td').forEach((td) => (td.innerHTML = '<br>'));

    if (where === 'below') row.after(newRow);
    else row.before(newRow);
  }

  private addColumn(where: 'left' | 'right'): void {
    const cell = this.getActiveTableCell();
    if (!cell) return;
    const colIndex = Array.from(cell.parentElement!.children).indexOf(cell);
    const shift = where === 'right' ? 1 : 0;

    const table = cell.closest('table')!;

    table.querySelectorAll('tr').forEach((tr) => {
      const newCell = document.createElement('td');
      newCell.className = 'border border-gray-300 dark:border-slate-600 p-1 min-w-[40px]'; // Apply styles to new cells

      // Apply theme classes to the new cell using ThemeManager
      ThemeManager.applyThemeToElement(newCell);

      newCell.innerHTML = '<br>';
      // Insert before the target column or at the end if it's the last column
      const targetNode = tr.children[colIndex + shift];
      if (targetNode) {
        tr.insertBefore(newCell, targetNode);
      } else {
        tr.appendChild(newCell);
      }
    });
  }

  private deleteRow(): void {
    const cell = this.getActiveTableCell();
    cell?.parentElement?.remove();
  }

  private deleteColumn(): void {
    const cell = this.getActiveTableCell();
    if (!cell) return;
    const colIndex = Array.from(cell.parentElement!.children).indexOf(cell);
    const table = cell.closest('table')!;
    table.querySelectorAll('tr').forEach((tr) => tr.children[colIndex]?.remove());
  }

  private deleteTable(): void {
    const cell = this.getActiveTableCell();
    cell?.closest('table')?.remove();
    this.miniToolbar?.remove();
    this.miniToolbar = undefined;
  }

  /* ---------------------- destroy --------------------- */

  public override destroy(): void {
    this.abort.abort();
    this.miniToolbar?.remove();
    super.destroy();
  }
}

/* -------------------------------------------------------------------------- */
/*                             Factory export                                 */
/* -------------------------------------------------------------------------- */

const tablePlugin = new TablePlugin();

export default tablePlugin;

/* -------------------------------------------------------------------------- */
/*                          Small helpers                                     */
/* -------------------------------------------------------------------------- */

// div helper is not used in this file after refactor, can be removed if not used elsewhere.
// function div(cls: string): HTMLDivElement {
//   return Object.assign(document.createElement('div'), { className: cls });
// }

function placeCaret(el: HTMLElement): void {
  const range = document.createRange();
  const sel = window.getSelection();

  if (!el.firstChild || el.innerHTML.trim() === '') {
      el.innerHTML = '<br>';
  }

  const targetNode = el.firstChild || el;
  // Ensure offset is within node length
  const offset = Math.min(0, targetNode.textContent?.length || 0);
  range.setStart(targetNode, offset);
  range.collapse(true);

  if (sel) {
      sel.removeAllRanges();
      sel.addRange(range);
  }
}
