/**
 * Collapsible Plugin for <PERSON><PERSON><PERSON><PERSON>  ▼
 * ───────────────────────────────────
 * Adds HTML `<details><summary>Title</summary>…</details>`
 * blocks with a toolbar button and an inline mini-toolbar for editing.
 *
 * • Toolbar “▼ Collapsible”
 *   – If text is **selected**, wraps it in a collapsible and asks for the
 *     summary text.
 *   – If no selection, inserts an empty boiler-plate block.
 *
 * • When caret is inside a `<details.feather-collapsible>` element, a floating
 *   bar appears with buttons to:
 *   ▸ Toggle open/closed
 *   ▸ Edit title
 *   ▸ Remove wrapper
 *
 * • Pure DOM APIs, no dependencies.  All listeners are removed on destroy().
 */

import type { Editor } from '../../types';
import { BasePlugin, PluginConfig } from '../base-plugin';
/* -------------------------------------------------------------------------- */
/*                                   Meta                                     */
/* -------------------------------------------------------------------------- */

const META: PluginConfig = {
  id:          'collapsible',
  name:        'Collapsible',
  description: '<details>/<summary> expandable sections',
  version:     '1.0.0',
  toolbarItems: [
    {
      id: 'collapsible',
      command: 'collapsible',
      icon: '▼',
      label: 'Collapsible',
      tooltip: 'Insert collapsible section',
      group: 'structure',
      ariaLabel: 'Insert collapsible'
    }
  ]
};

export const pluginId = META.id;

/* -------------------------------------------------------------------------- */
/*                                The plugin                                  */
/* -------------------------------------------------------------------------- */

class CollapsiblePlugin extends BasePlugin {
  private abort = new AbortController();
  private miniToolbar?: HTMLElement;

  constructor() {
    super(META);
  }

  /* ----------------------- Base hooks ---------------------- */

  protected override onInit(): void {
    document.addEventListener(
      'selectionchange',
      () => this.refreshMiniToolbar(),
      { signal: this.abort.signal }
    );
  }

  public override handleCommand(cmd: string): void {
    if (cmd === 'collapsible') this.insertOrWrapCollapsible();
  }

  /* ---------------------- Insert / wrap -------------------- */

  private insertOrWrapCollapsible(): void {
    if (!this.editor) return;
    const sel = window.getSelection();
    const hasSelection = sel && !sel.isCollapsed && sel.rangeCount > 0;
    const title =
      prompt('Collapsible title:', 'Details') ?? 'Details';

    const details = document.createElement('details');
    details.className = 'feather-collapsible group border border-gray-300 dark:border-slate-600 rounded p-1 my-[6px] bg-transparent text-gray-900 dark:text-slate-200 open:pb-2';

    // Apply theme classes to the details element
    const htmlElement = document.documentElement;
    const storedTheme = localStorage.getItem('editor-theme');
    if (htmlElement.classList.contains('dark') || storedTheme === 'dark') {
      details.classList.add('dark');
    }
    if (storedTheme) {
      details.classList.add(`theme-${storedTheme}`);
    }

    // Listen for theme changes
    document.addEventListener('themechange', (e) => {
      const theme = (e as CustomEvent).detail;
      // Remove existing theme classes
      details.classList.remove('theme-light', 'theme-dark', 'dark');
      // Add new theme class
      details.classList.add(`theme-${theme}`);
      if (theme === 'dark') {
        details.classList.add('dark');
      }
    }, { signal: this.abort.signal });

    const summary = document.createElement('summary');
    summary.className = 'cursor-pointer font-semibold py-1 list-none relative focus:outline-none focus-visible:outline-2 focus-visible:outline-blue-500 focus-visible:outline-offset-2';

    const markerSpan = document.createElement('span');
    markerSpan.className = 'inline-block w-4 mr-2 transition-transform duration-200 ease-in-out group-open:rotate-90';
    markerSpan.textContent = '▼';
    summary.appendChild(markerSpan);
    summary.appendChild(document.createTextNode(title));

    details.appendChild(summary);

    if (hasSelection) {
      const range = sel!.getRangeAt(0);
      const fragment = range.extractContents();
      details.appendChild(fragment);
      range.insertNode(details);
    } else {
      /* boiler-plate paragraph */
      const para = document.createElement('p');
      para.innerHTML = '<br>';
      details.appendChild(para);

      /* insert at caret or end */
      if (sel && sel.rangeCount) {
        sel.getRangeAt(0).insertNode(details);
      } else {
        this.editor.getElement().appendChild(details);
      }
    }

    /* place caret inside first content node */
    const target =
      details.querySelector(':scope > :not(summary)') ?? details;
    placeCaret(target as HTMLElement);
  }

  /* ----------------- Floating mini-toolbar ----------------- */

  private refreshMiniToolbar(): void {
    const details = this.activeCollapsible();
    if (!details) {
      this.miniToolbar?.remove();
      this.miniToolbar = undefined;
      return;
    }

    if (!this.miniToolbar) {
      this.miniToolbar = this.buildMiniToolbar();
      document.body.appendChild(this.miniToolbar);
    }

    /* position above the summary element */
    const rect = details.querySelector('summary')!.getBoundingClientRect();
    this.miniToolbar.style.left = `${rect.left + window.scrollX}px`;
    this.miniToolbar.style.top = `${rect.top + window.scrollY - 34}px`;
    this.miniToolbar.classList.toggle(
      'open',
      details.hasAttribute('open')
    );
  }

  private buildMiniToolbar(): HTMLElement {
    const bar = document.createElement('div');
    bar.className = 'absolute flex gap-1 p-1 bg-white dark:bg-slate-700 border border-gray-300 dark:border-slate-600 rounded shadow-md z-[100]';

    const makeBtn = (title: string, label: string, fn: () => void) => {
      const b = document.createElement('button');
      b.type = 'button';
      b.className = 'border border-gray-400 dark:border-slate-500 bg-gray-100 dark:bg-slate-600 rounded-sm px-[6px] py-[2px] cursor-pointer text-xs text-gray-700 dark:text-slate-200 hover:bg-gray-200 dark:hover:bg-slate-500 hover:border-gray-500 dark:hover:border-slate-400 focus:outline-none';
      b.title = title;
      b.textContent = label;
      b.addEventListener('click', (e) => {
        e.preventDefault();
        fn();
      });
      bar.appendChild(b);
    };

    makeBtn('Toggle open/closed', '⤵︎', () => {
      const det = this.activeCollapsible();
      if (det) det.open = !det.open;
    });

    makeBtn('Edit title', '✎', () => {
      const det = this.activeCollapsible();
      if (!det) return;
      const summary = det.querySelector('summary')!;
      const newTitle = prompt('New title:', summary.textContent ?? '') ?? '';
      if (newTitle) summary.textContent = newTitle;
    });

    makeBtn('Remove collapsible wrapper', '🗑', () => {
      const det = this.activeCollapsible();
      if (!det) return;
      /* unwrap: move children before removing details */
      while (det.firstChild) {
        const node = det.firstChild;
        if (node.nodeName === 'SUMMARY') {
          det.removeChild(node);
        } else {
          det.parentNode!.insertBefore(node, det);
        }
      }
      det.remove();
      this.miniToolbar?.remove();
      this.miniToolbar = undefined;
    });

    return bar;
  }

  private activeCollapsible(): HTMLDetailsElement | null {
    const sel = window.getSelection();
    if (!sel || sel.rangeCount === 0) return null;
    const node = sel.anchorNode as Node;
    return (node instanceof HTMLElement
      ? node.closest('details.feather-collapsible')
      : node.parentElement?.closest('details.feather-collapsible')) as
      | HTMLDetailsElement
      | null;
  }

  /* ---------------------- destroy ---------------------- */

  public override destroy(): void {
    this.abort.abort();
    this.miniToolbar?.remove();
    super.destroy();
  }
}

/* -------------------------------------------------------------------------- */
/*                               Factory                                      */
/* -------------------------------------------------------------------------- */

const collapsiblePlugin = new CollapsiblePlugin();

export default collapsiblePlugin;

/* -------------------------------------------------------------------------- */
/*                       Utility helpers (private)                            */
/* -------------------------------------------------------------------------- */



function placeCaret(el: HTMLElement): void {
  const range = document.createRange();
  range.selectNodeContents(el);
  range.collapse(true);
  const sel = window.getSelection();
  sel!.removeAllRanges();
  sel!.addRange(range);
}
