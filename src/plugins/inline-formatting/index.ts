import type { Editor, Plugin } from '../../types';
// Import all plugins
import boldPlugin from './bold';
import italicPlugin from './italic';
import underlinePlugin from './underline';
import strikethroughPlugin from './strikethrough';
import superscriptPlugin from './superscript';
import subscriptPlugin from './subscript';
import inlineCodePlugin from './inline-code';
import textColorPlugin from './text-color';
import clearFormatPlugin from './clear-format';

// This file serves as the entry point for all inline formatting plugins
// Each plugin is exported individually to ensure tree-shaking works properly

// Export individual plugins
export { 
  boldPlugin, 
  italicPlugin, 
  underlinePlugin,
  strikethroughPlugin,
  superscriptPlugin,
  subscriptPlugin,
  inlineCodePlugin,
  textColorPlugin,
  clearFormatPlugin
};

// Export a combined plugin group for convenience
export const InlineFormattingPlugins: Plugin[] = [
  boldPlugin,
  italicPlugin,
  underlinePlugin,
  strikethroughPlugin,
  superscriptPlugin,
  subscriptPlugin,
  inlineCodePlugin,
  textColorPlugin,
  clearFormatPlugin
];

// Export the plugins array directly
// PluginManager will handle individual plugin registration
export default InlineFormattingPlugins;
